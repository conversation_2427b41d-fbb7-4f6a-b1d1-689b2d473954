.promotion-slider {
  margin-top: 150px; 
  width: 100%;
  height: auto;
  min-height: 350px; 
  overflow: hidden;
  position: relative;
  background: linear-gradient(to right, rgba(189, 154, 160, 0.75), rgba(177, 136, 144, 0.65));
  border-radius: 16px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
}

.slider-container {
  display: flex;
  transition: transform 0.5s ease-in-out;
  width: 100%;
  height: 100%;
}

.slide {
  min-width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.6rem; 
  padding: 0.8rem 3%; 
  height: 100%;
  justify-content: space-between; 
}

.slide-image {
  max-width: 35%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.slide-image img {
  width: 85%; 
  max-width: 180px; 
  height: auto;
  border-radius: 20px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
  object-fit: contain;
  margin: 0; 
}

.catch-phrase {
  font-size: 1.1rem; 
  color: #f8ae9d;
  font-weight: 600;
  margin-top: 0.4rem;
  text-align: center;
  font-style: italic;
}

.slide-content {
  flex: 1;
  max-width: 60%;
  background-color: white;
  padding: 0.8rem 1rem; 
  border-radius: 12px;
  box-shadow: 0 3px 10px rgba(0,0,0,0.06);
  display: flex;
  flex-direction: column;
  justify-content: center; 
}

.slide-content h3 {
  font-size: 1.4rem;
  color: #f8ae9d;
  margin-bottom: 0.4rem;
  margin-top: 0;
  font-weight: 600;
  line-height: 1.3; 
}

.slide-content p {
  font-size: 1rem; 
  color: #5a4a42;
  margin-bottom: 0.5rem;
  line-height: 1.3;
  margin-top: 0;
  max-height: 4rem; 
  overflow: hidden; 
}

.cta-button {
  background-color: #d4a59a;
  color: white;
  border: none;
  padding: 0.8rem 2rem;
  border-radius: 25px; 
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.cta-button:hover {
  background-color: #c38b7d;
  transform: translateY(-2px);
  box-shadow: 0 2px 10px rgba(195, 139, 125, 0.4);
}

.slider-dots {
  display: flex;
  justify-content: center;
  margin-top: 1.5rem;
  gap: 0.5rem;
}

.dot {
  width: 10px;
  height: 10px;
  border-radius: 50%; 
  background-color: #d4a59a;
  opacity: 0.3;
  cursor: pointer;
  border: none;
  transition: all 0.3s ease;
}

.dot.active {
  opacity: 1;
  transform: scale(1.2);
}


@media (max-width: 1200px) {
  .promotion-slider {
    margin-top: 140px;
    min-height: 330px;
  }
}

@media (max-width: 992px) {
  .promotion-slider {
    margin-top: 130px;
    min-height: 320px;
  }
  
  .slide {
    padding: 0.7rem 2%;
    gap: 0.4rem;
  }
  
  .slide-content {
    padding: 0.7rem;
  }
}

@media (max-width: 768px) {
  .promotion-slider {
    margin-top: 80px;
    min-height: 420px; 
  }
  
  .slide {
    gap: 0.3rem;
    padding: 0.7rem;
  }
  
  .slide-content {
    padding: 0.8rem;
    max-width: 90%;
  }
  
  .slide-image {
    max-width: 90%;
  }
  
  .slide-image img {
    max-width: 160px;
  }
  
  .slide-content h3 {
    font-size: 1.3rem;
    margin-bottom: 0.3rem;
  }
  
  .slide-content p {
    font-size: 0.85rem;
    margin-bottom: 0.5rem;
  }
  
  .catch-phrase {
    font-size: 0.9rem;
    margin-top: 0.3rem;
  }
}

@media (max-width: 576px) {
  .promotion-slider {
    margin-top: 120px;
    min-height: 550px;
  }
  
  .slide {
    padding: 1.5rem;
  }
  
  .slide-content {
    padding: 1.2rem;
    max-width: 95%;
  }
  
  .slide-image img {
    width: 80%;
  }
}

@media (max-width: 480px) {
  .promotion-slider {
    margin-top: 100px;
    min-height: 500px;
  }
  
  .slide-content {
    padding: 1rem;
  }
  
  .slide-content h3 {
    font-size: 1.3rem;
    margin-bottom: 0.5rem;
  }
  
  .slide-content p {
    font-size: 0.9rem;
    margin-bottom: 1rem;
  }
  
  .cta-button {
    padding: 0.6rem 1.5rem;
    font-size: 0.9rem;
  }
  
  .slider-dots {
    margin-top: 1rem;
  }
  
  .dot {
    width: 8px;
    height: 8px;
  }
}

@media (max-width: 360px) {
  .promotion-slider {
    margin-top: 80px;
    min-height: 450px;
  }
  
  .slide {
    padding: 1rem;
  }
  
  .slide-image img {
    width: 90%;
  }
  
  .slide-content {
    padding: 0.8rem;
  }
}


@media (max-width: 320px) {
  .promotion-slider {
    margin-top: 60px;
    min-height: 400px;
  }
  
  .slide {
    padding: 0.8rem;
  }
  
  .slide-content {
    padding: 0.6rem;
  }
  
  .slide-content h3 {
    font-size: 1.1rem;
  }
  
  .slide-content p {
    font-size: 0.8rem;
  }
  
  .cta-button {
    padding: 0.5rem 1.2rem;
    font-size: 0.8rem;
  }
}
