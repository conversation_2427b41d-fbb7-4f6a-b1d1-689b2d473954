{"name": "sipping-pretty-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@fortawesome/fontawesome-free": "^6.7.2", "@react-google-maps/api": "^2.20.6", "@reduxjs/toolkit": "^2.8.2", "axios": "^1.9.0", "bootstrap": "^5.3.6", "date-fns": "^4.1.0", "jwt-decode": "^4.0.0", "react": "^19.1.0", "react-bootstrap": "^2.10.10", "react-data-table-component": "^7.7.0", "react-datepicker": "^8.4.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-redux": "^9.2.0", "react-router-dom": "^7.6.0", "redux": "^5.0.1"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "vite": "^6.3.5"}}