@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');
body {
    font-family:'Poppins',sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background-color: aliceblue;
  }
  
  code {
    font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
      monospace;
  }
*{
    margin:0;
    padding:0;
    box-sizing:border-box;
  }
.Wrapper{
    width: 420px;
    background:transparent;
    color: white;  
    border-radius: 10px;
    padding: 30px 40px; 
}
.Wrapper h1{
    text-align: center;
    font-size: 30px;
}
.Wrapper .inputbox{
    position:relative;
    width: 100%;
    height: 50px;
    margin: 30px 0;
    color: black;
}
.inputbox input{
    width: 100%;
    height:100%;
    background: transparent;
    border: none;
    outline:none;
    border: 2px solid rgba(255, 255, 255, .2);
    border-radius: 40px;
   font-size: 16px;
   color: rgb(53, 51, 51);
   padding: 20px 45px 20px 20px;
}
.inputbox input::placeholder{
    color: rgb(20, 19, 19);
    text-align: center;
    font-size: 15px;
}
.inputbox .icon{
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    font: 18px;
}
.Wrapper .RemembermeBox-forget{
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    margin: -15px 0 15px;
}
.Wrapper .registerLink{
    display: flex;
    font-size: 16px;
    padding-left: 50px;
    margin: 20px 0 15px;
}
.Wrapper .registerLink p a{
    text-decoration: none;
    color: white;
    font-weight: 600;
}
.Wrapper .registerLink a:hover{
    text-decoration: underline;
}
.RemembermeBox-forget label input{
    accent-color: white;
    margin-right: 2px;
}
.RemembermeBox-forget a{
    text-decoration: none;
    color: white;
}
.RemembermeBox-forget a:hover{
    text-decoration: underline;
}
.Wrapper button{
    width: 100%;
    font-size: 16px;
    height: 40px;
    font-weight: 800;
    border: none;
    cursor: pointer;
    border-radius: 45px;
    background-color: white;
    outline: none;

}
