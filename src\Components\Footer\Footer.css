
.footer {
  background: linear-gradient(135deg,
    rgba(68, 41, 63, 0.96) 0%,
    rgba(94, 65, 79, 0.94) 50%,
    rgba(108, 70, 85, 0.96) 100%);
  color: #e8d4e3;
  position: relative;
  overflow: hidden;
  -webkit-backdrop-filter: blur(25px);
  backdrop-filter: blur(25px);
  width: 100%;
   margin-top:150px;
  flex-shrink: 0;
  border-top: 1px solid rgba(248, 187, 208, 0.15);
  box-shadow: 0 -4px 20px rgba(68, 41, 63, 0.3);
  padding: 40px 0;
  
}

/* Add a subtle animated background pattern */
.footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent,
    rgba(248, 187, 208, 0.03),
    transparent);
  animation: shimmer 8s ease-in-out infinite;
  pointer-events: none;
  transform: translateX(-100%);
}

@keyframes shimmer {
  0%, 100% { transform: translateX(-100%); }
  50% { transform: translateX(100%); }
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  width: 100%;
  box-sizing: border-box;
  margin-top:15px;
}

.footer-content {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1.5fr;
  gap: 24px;
  padding: 40px 0 24px 0;
  position: relative;
}


.brand-section {
  max-width: 380px;
}

.footer-logo h2 {
  font-family: 'Pacifico', cursive;
  font-size: 1.5rem;
  color: #f8bbd0;
  margin: 0 0 8px 0;
  text-shadow: 0 2px 6px rgba(0, 0, 0, 0.4);
  letter-spacing: 0.5px;
}

.logo-accent {
  width: 50px;
  height: 3px;
  background: linear-gradient(90deg, #cc4068, #f8bbd0, #cc4068);
  border-radius: 3px;
  margin-bottom: 16px;
  box-shadow: 0 2px 4px rgba(204, 64, 104, 0.3);
}

.footer-description {
  font-family: 'Poppins', sans-serif;
  font-size: 0.85rem;
  line-height: 1.6;
  color: #d4a1ba;
  margin-bottom: 20px;
  font-weight: 300;
}


.social-links {
  display: flex;
  gap: 16px;
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  background: linear-gradient(135deg, rgba(204, 64, 104, 0.25), rgba(248, 187, 208, 0.15));
  border: 1px solid rgba(204, 64, 104, 0.4);
  border-radius: 14px;
  color: #f8bbd0;
  font-size: 1.1rem;
  text-decoration: none;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  -webkit-backdrop-filter: blur(15px);
  backdrop-filter: blur(15px);
  position: relative;
  overflow: hidden;
}

.social-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.social-link:hover::before {
  left: 100%;
}

.social-link:hover {
  background: linear-gradient(135deg, rgba(204, 64, 104, 0.5), rgba(248, 187, 208, 0.3));
  border-color: rgba(248, 187, 208, 0.7);
  color: #ffffff;
  transform: translateY(-4px) scale(1.08);
  box-shadow: 0 10px 30px rgba(204, 64, 104, 0.5);
}


.footer-section h3.footer-title {
  font-family: 'Poppins', sans-serif;
  font-size: 1rem;
  font-weight: 600;
  color: #f8bbd0;
  margin: 0 0 12px 0;
  position: relative;
}

.footer-title::after {
  content: '';
  position: absolute;
  bottom: -6px;
  left: 0;
  width: 30px;
  height: 2px;
  background: linear-gradient(90deg, #cc4068, transparent);
  border-radius: 1px;
}


.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li {
  margin-bottom: 8px;
}

.footer-link {
  font-family: 'Poppins', sans-serif;
  font-size: 0.8rem;
  color: #d4a1ba;
  text-decoration: none;
  transition: all 0.3s ease;
  display: inline-block;
  position: relative;
}

.footer-link:hover {
  color: #f8bbd0;
  transform: translateX(4px);
}

.footer-link::before {
  content: '';
  position: absolute;
  left: -12px;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 4px;
  background: #cc4068;
  border-radius: 50%;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.footer-link:hover::before {
  opacity: 1;
}


.contact-info {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.contact-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  font-family: 'Poppins', sans-serif;
  font-size: 0.8rem;
  color: #d4a1ba;
  line-height: 1.4;
}

.contact-icon {
  color: #cc4068;
  font-size: 1rem;
  margin-top: 2px;
  min-width: 16px;
}


.newsletter-section {
  background: rgba(204, 64, 104, 0.1);
  border: 1px solid rgba(204, 64, 104, 0.2);
  border-radius: 12px;
  padding: 15px; /* Reduced from 20px */
  margin: 0 0 16px 0; /* Reduced from 0 0 24px 0 */
  text-align: center;
}

.newsletter-content h3 {
  font-family: 'Poppins', sans-serif;
  font-size: 1.1rem;
  font-weight: 600;
  color: #f8bbd0;
  margin: 0 0 6px 0;
}

.newsletter-content p {
  font-family: 'Poppins', sans-serif;
  font-size: 0.8rem;
  color: #d4a1ba;
  margin: 0 0 16px 0;
  line-height: 1.4;
}

.newsletter-form {
  display: flex;
  gap: 8px;
  max-width: 320px;
  margin: 0 auto;
}

.newsletter-input {
  flex: 1;
  padding: 8px 12px;
  border: 1.5px solid rgba(204, 64, 104, 0.3);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  color: #e8d4e3;
  font-family: 'Poppins', sans-serif;
  font-size: 0.8rem;
  transition: all 0.3s ease;
}

.newsletter-input::placeholder {
  color: #b8a1b0;
}

.newsletter-input:focus {
  outline: none;
  border-color: rgba(204, 64, 104, 0.6);
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 0 0 3px rgba(204, 64, 104, 0.1);
}

.newsletter-button {
  padding: 8px 16px;
  background: linear-gradient(135deg, #cc4068, #d4527a);
  border: none;
  border-radius: 8px;
  color: white;
  font-family: 'Poppins', sans-serif;
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  display: flex;
  align-items: center;
  gap: 6px;
  white-space: nowrap;
}

.newsletter-button:hover {
  background: linear-gradient(135deg, #b8365a, #c04468);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(204, 64, 104, 0.4);
}


.footer-bottom {
  border-top: 1px solid rgba(248, 187, 208, 0.2);
  padding: 20px 0;
  background: linear-gradient(135deg, rgba(68, 41, 63, 0.3), rgba(94, 65, 79, 0.2));
  margin-top: 20px;
}

.footer-bottom-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.copyright p {
  font-family: 'Poppins', sans-serif;
  font-size: 0.9rem;
  color: #c4a1b4;
  margin: 0;
  font-weight: 300;
}

.footer-bottom-links {
  display: flex;
  gap: 28px;
}

.footer-bottom-link {
  font-family: 'Poppins', sans-serif;
  font-size: 0.9rem;
  color: #c4a1b4;
  text-decoration: none;
  transition: all 0.3s ease;
  position: relative;
  font-weight: 300;
}

.footer-bottom-link::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 1px;
  background: linear-gradient(90deg, #cc4068, #f8bbd0);
  transition: width 0.3s ease;
}

.footer-bottom-link:hover {
  color: #f8bbd0;
}

.footer-bottom-link:hover::after {
  width: 100%;
}


@media (max-width: 1024px) {
  .footer-container {
    padding: 0 16px;
  }

  .footer-content {
    grid-template-columns: 1fr 1fr;
    gap: 24px;
    padding: 28px 0 20px 0;
  }

  .brand-section {
    max-width: none;
  }
}

@media (max-width: 768px) {
  .footer-container {
    padding: 0 16px;
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: 32px;
    padding: 40px 0 30px 0;
    text-align: center;
  }

  .brand-section {
    max-width: none;
  }

  .footer-logo h2 {
    font-size: 2rem;
  }

  .social-links {
    justify-content: center;
  }

  .newsletter-section {
    padding: 24px;
    margin: 0 0 30px 0;
  }

  .newsletter-form {
    flex-direction: column;
    max-width: 300px;
  }

  .newsletter-button {
    justify-content: center;
  }

  .footer-bottom-content {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .footer-bottom-links {
    gap: 16px;
  }
}

@media (max-width: 480px) {
  .footer-container {
    padding: 0 12px;
  }

  .footer-content {
    gap: 24px;
    padding: 32px 0 24px 0;
  }

  .footer-logo h2 {
    font-size: 1.8rem;
  }

  .footer-description {
    font-size: 0.9rem;
  }

  .footer-title {
    font-size: 1.1rem;
  }

  .footer-link {
    font-size: 0.85rem;
  }

  .contact-item {
    font-size: 0.85rem;
  }

  .social-links {
    gap: 8px;
  }

  .social-link {
    width: 42px;
    height: 42px;
    font-size: 1.05rem;
  }

  .newsletter-section {
    padding: 20px;
    border-radius: 12px;
  }

  .newsletter-content h3 {
    font-size: 1.2rem;
  }

  .newsletter-content p {
    font-size: 0.9rem;
  }

  .newsletter-input,
  .newsletter-button {
    padding: 10px 14px;
    font-size: 0.85rem;
  }

  .footer-bottom {
    padding: 20px 0;
  }

  .copyright p,
  .footer-bottom-link {
    font-size: 0.8rem;
  }

  .footer-bottom-links {
    flex-wrap: wrap;
    justify-content: center;
    gap: 12px;
  }
}









