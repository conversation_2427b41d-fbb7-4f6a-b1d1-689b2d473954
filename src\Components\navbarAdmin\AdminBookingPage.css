.admin-booking-container {
  max-width: 1100px;
  margin: 2rem auto;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 15px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
  font-family: "Poppins", sans-serif;
}

.admin-booking-container h2 {
  font-family: "Pacifico", cursive;
  font-size: 2.2rem;
  color: #5e412f;
  margin-bottom: 1.5rem;
  text-align: center;
}

.booking-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0 8px;
}

.booking-table th {
  padding: 12px 16px;
  text-align: left;
  font-weight: 600;
  color: #5e412f;
  border-bottom: 2px solid #f8bbd0;
}

.booking-table td {
  padding: 12px 16px;
  background: white;
}

.booking-table tr td:first-child {
  border-top-left-radius: 8px;
  border-bottom-left-radius: 8px;
}

.booking-table tr td:last-child {
  border-top-right-radius: 8px;
  border-bottom-right-radius: 8px;
}

.booking-table .delete-btn {
  padding: 8px 16px;
  background-color: #d9534f;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.booking-table .delete-btn:hover {
  background-color: #c9302c;
}

/* Responsive styles */
@media (max-width: 768px) {
  .admin-booking-container {
    padding: 1.5rem;
    margin: 1.5rem;
  }

  .admin-booking-container h2 {
    font-size: 1.8rem;
  }
}

@media (max-width: 480px) {
  .admin-booking-container {
    padding: 1rem;
    margin: 1rem;
  }

  .admin-booking-container h2 {
    font-size: 1.5rem;
  }

  .booking-table {
    display: block;
    overflow-x: auto;
    white-space: nowrap;
  }

  .booking-table th,
  .booking-table td {
    padding: 8px 12px;
  }
}
