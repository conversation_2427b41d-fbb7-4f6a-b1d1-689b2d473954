This file explains how Visual Studio created the project.

The following tools were used to generate this project:
- create-vite

The following steps were used to generate this project:
- Create react project with create-vite: `npm init --yes vite@latest sipping-pretty-frontend -- --template=react`.
- Updating `vite.config.js` with port.
- Create project file (`sipping-pretty-frontend.esproj`).
- Create `launch.json` to enable debugging.
- Add project to solution.
- Write this file.
