.payment-container {
  max-width: 500px;
  margin: 4rem auto;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
  font-family: "Poppins", sans-serif;
  animation: pop-in 0.4s ease;
}

.payment-header h2 {
  font-family: "Pacifico", cursive;
  font-size: 2rem;
  color: #6d4c41;
  margin-bottom: 0.3rem;
  text-align: center;
}

.payment-header p {
  text-align: center;
  font-size: 0.95rem;
  color: #775b5b;
  margin-bottom: 1.5rem;
}


.payment-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.payment-form label {
  font-weight: 500;
  color: #4e342e;
  margin-bottom: 4px;
}

.payment-form input,
.payment-form select {
  padding: 10px 14px;
  border: 1px solid #ddd;
  border-radius: 10px;
  font-size: 14px;
  outline: none;
  transition: border 0.2s;
}

.payment-form input:focus,
.payment-form select:focus {
  border-color: #f48fb1;
}


.card-row {
  display: flex;
  gap: 12px;
}

.card-row input {
  flex: 1;
}


.qr-section {
  text-align: center;
  margin: 2rem 0 1.5rem 0;
}

.qr-section img {
  width: 180px;
  height: 180px;
  border: 4px solid #f8bbd0;
  border-radius: 15px;
}

.qr-section p {
  margin-top: 10px;
  font-size: 14px;
  color: #6e4c4c;
}


.pay-btn {
  background: linear-gradient(to right, #f8bbd0, #f9c9d4);
  border: none;
  padding: 0.75rem 2rem;
  font-size: 1rem;
  color: #4e342e;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
  box-shadow: 0 6px 12px rgba(249, 168, 212, 0.3);
}

.pay-btn:hover {
  background: linear-gradient(to right, #f48fb1, #f9a8d4);
  box-shadow: 0 8px 16px rgba(249, 168, 212, 0.5);
}


.back-btn {
  margin-top: 1rem;
  color: #5d4037;
  text-align: center;
  cursor: pointer;
  text-decoration: underline;
  font-size: 0.9rem;
}


@media (max-width: 480px) {
  .payment-container {
    margin: 2rem 1rem;
    padding: 1.5rem;
  }

  .card-row {
    flex-direction: column;
  }
}


.card-input-container {
  position: relative;
  display: flex;
  flex-direction: column;
}


.card-type {
  position: absolute;
  right: 12px;
  top: 38px;
  background-color: #f0f0f0;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  color: #4e342e;
}


.input-error {
  border-color: #e53935 !important;
  background-color: #ffebee;
}

.error-message {
  color: #e53935;
  font-size: 12px;
  margin-top: 4px;
  font-weight: 500;
}


.payment-summary {
  background-color: #f9f4f5;
  border-radius: 10px;
  padding: 15px;
  margin: 15px 0;
  border: 1px solid #f8bbd0;
}

.payment-summary h3 {
  margin-top: 0;
  margin-bottom: 12px;
  font-size: 16px;
  color: #6d4c41;
  text-align: center;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
  color: #5d4037;
}

.summary-row.total {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px dashed #f8bbd0;
  font-weight: 700;
  font-size: 16px;
  color: #4e342e;
}
.order-type-selection {
  display: flex;
  justify-content: space-between;
  margin: 1rem 0;
  font-weight: 500;
  color: #4e342e;
}
.order-type-selection label {
  display: flex;
  align-items: center;
  gap: 8px;
}
.delivery-fields input {
  padding: 10px 14px;
  border: 1px solid #ddd;
  border-radius: 10px;
  font-size: 14px;
  outline: none;
  width: 100%;
  margin-bottom: 1rem;
  box-sizing: border-box;
  transition: border 0.2s;
}

.delivery-fields input:focus {
  border-color: #f48fb1;
}
