

.booking-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 2rem 1rem;
  background: linear-gradient(to right, rgba(248, 187, 208, 0.1), rgba(255, 255, 255, 0.8));
  border-radius: 15px;
  margin: 1.5rem auto;
  max-width: 1200px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
  text-align: center;
}


.booking-section::before {
  content: "";
  position: absolute;
  top: -50px;
  right: -50px;
  width: 200px;
  height: 200px;
  background: radial-gradient(circle, rgba(248, 187, 208, 0.15) 0%, rgba(255,255,255,0) 70%);
  z-index: 0;
}

.left-side {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  max-width: 100%;
  position: relative;
  z-index: 1;
  align-items: center;
}

.tagline h2 {
  font-family: 'Pacifico', cursive;
  font-size: 1.8rem;
  color: #5e412f;
  margin-bottom: 0.5rem;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
  animation: fadeInUp 0.8s ease-out;
}

.tagline p {
  font-family: 'Poppins', sans-serif;
  font-size: 1.2rem;
  color: #5e412f;
  margin: 0;
  line-height: 1.6;
  animation: fadeInUp 0.8s ease-out 0.2s both;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  animation: fadeInUp 0.8s ease-out 0.4s both;
  width: 100%;
}

.shop-button, .book-button {
  width: 100%;
  padding: 0.8rem 1.5rem;
  font-size: 0.9rem;
  border: none;
  border-radius: 25px;
  font-family: 'Poppins', sans-serif;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.book-button {
  background: #f8bbd0;
  color: #5e412f;
}

.shop-button {
  background: #5e412f;
  color: white;
}

.book-button:hover {
  background: #f48fb1;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(244, 143, 177, 0.3);
}

.shop-button:hover {
  background: #3e2a1f;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(94, 65, 47, 0.3);
}

/* Button hover effects */
.shop-button::after, .book-button::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255,255,255,0.1);
  transform: translateX(-100%);
  transition: transform 0.3s ease;
}

.shop-button:hover::after, .book-button:hover::after {
  transform: translateX(0);
}

.search-bar {
  animation: fadeInUp 0.8s ease-out 0.6s both;
}

.search-bar input {
  width: 100%;
  max-width: 100%;
  padding: 0.8rem 1.5rem;
  border: 1px solid #e0c8cf;
  border-radius: 25px;
  font-family: 'Poppins', sans-serif;
  transition: all 0.3s ease;
}

.search-bar input:focus {
  outline: none;
  border-color: #f48fb1;
  box-shadow: 0 0 0 3px rgba(244, 143, 177, 0.2);
}

.right-side {
  margin-top: 2rem;
  max-width: 100%;
  display: flex;
  justify-content: center;
  perspective: 1000px;
  position: relative;
  z-index: 1; 
}

.coffee-mug {
  max-width: 100%;
  height: auto;
  max-height: 200px;
  animation: spin 8s linear infinite;
  transform-origin: center center;
  filter: drop-shadow(0 8px 15px rgba(0, 0, 0, 0.15));
  will-change: transform;
  transition: all 0.5s ease;
  position: relative;
  z-index: 1; 
}


@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}


.coffee-mug::before {
  content: "";
  position: absolute;
  top: -40px;
  left: 50%;
  width: 60px;
  height: 80px;
  background: rgba(255,255,255,0.8);
  border-radius: 50%;
  filter: blur(15px);
  transform: translateX(-50%);
  opacity: 0;
  animation: steam 6s ease-in-out infinite;
}

@keyframes steam {
  0%, 100% {
    opacity: 0;
    transform: translateX(-50%) translateY(0) scale(0.8);
  }
  50% {
    opacity: 0.6;
    transform: translateX(-50%) translateY(-20px) scale(1);
  }
}


@media (min-width: 768px) {
  .booking-section {
    flex-direction: row;
    justify-content: space-between;
    padding: 3rem 2rem;
    text-align: left;
  }
  
  .left-side {
    max-width: 50%;
    padding-right: 2rem;
    align-items: flex-start;
  }
  
  .tagline h2 {
    font-size: 2.5rem;
  }
  
  .action-buttons {
    flex-direction: row;
    width: auto;
  }
  
  .shop-button, .book-button {
    width: auto;
    padding: 0.8rem 2rem;
    font-size: 1rem;
  }
  
  .search-bar input {
    max-width: 300px;
  }
  
  .right-side {
    margin-top: 0;
    max-width: 50%;
  }
  
  .coffee-mug {
    max-height: 400px;
  }
}
