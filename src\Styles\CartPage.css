.cart-page {
  padding: 20px;
  max-width: 800px;
  margin: auto;
  font-family: Arial, sans-serif;
  background-color: #f9f9f9;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.cart-page h1 {
  text-align: center;
  color: #333;
  margin-bottom: 30px;
}

.cart-items-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.cart-item {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 16px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background-color: #fff;
}

.cart-item-image {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 6px;
}

.cart-item-details {
  flex-grow: 1;
}

.cart-item-details h3 {
  margin: 0 0 5px;
  font-size: 1.2rem;
  color: #222;
}

.cart-item-details p {
  margin: 0;
  color: #555;
}

.cart-item-price {
  font-weight: bold;
  color: #4caf50;
  font-size: 1.1rem;
}

.empty-cart {
  text-align: center;
  color: #777;
  font-size: 1.2rem;
}

.checkout-btn {
  margin-top: 20px;
  padding: 12px 24px;
  font-size: 1rem;
  background-color: #2e8b57;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.checkout-btn:hover {
  background-color: #256d45;
}
