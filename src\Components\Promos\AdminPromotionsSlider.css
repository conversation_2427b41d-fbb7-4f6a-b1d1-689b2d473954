@import "./PromotionSlider.css";

/* Ensure consistent spacing for action button */
.slide-content .cta-button {
  margin-top: 0.8rem;
}

/* Modal overlay and box styling */
.custom-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  width: 100vw;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.custom-modal {
  background-color: white;
  padding: 30px;
  width: 400px;
  min-height: 200px;
  border-radius: 10px;
  z-index: 10000;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

/* Inputs and textareas inside modal */
.custom-modal input,
.custom-modal textarea {
  padding: 10px;
  border-radius: 6px;
  border: 1px solid #ccc;
  font-size: 1rem;
  width: 100%;
  resize: none;
  margin-bottom: 10px;
}

/* Modal button row */
.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* Use the same .cta-button class for all modal actions */
.modal-actions .cta-button {
  padding: 0.8rem 2rem;
  font-size: 1rem;
  border-radius: 25px;
  border: none;
  font-weight: 500;
  cursor: pointer;
  background-color: #d4a59a;
  color: white;
  transition: all 0.3s ease;
}

.modal-actions .cta-button:hover {
  background-color: #c38b7d;
  transform: translateY(-2px);
  box-shadow: 0 2px 10px rgba(195, 139, 125, 0.4);
}

/* Reusable button group for Add, Edit, Delete */
.button-group {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}

.button-group .cta-button {
  padding: 0.8rem 2rem;
  font-size: 1rem;
  border-radius: 25px;
  border: none;
  font-weight: 500;
  cursor: pointer;
  background-color: #d4a59a;
  color: white;
  transition: all 0.3s ease;
}

.button-group .cta-button:hover {
  background-color: #c38b7d;
  transform: translateY(-2px);
  box-shadow: 0 2px 10px rgba(195, 139, 125, 0.4);
}
