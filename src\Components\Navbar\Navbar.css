* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Professional navbar animations and effects */
@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.navbar {
  background: linear-gradient(
    135deg,
    rgba(68, 41, 63, 0.95),
    rgba(108, 70, 85, 0.9)
  );
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 85px;
  width: 95%;
  color: #dbc0d3;
  box-shadow: 0 4px 20px rgba(51, 34, 46, 0.25), 0 1px 3px rgba(0, 0, 0, 0.1);
  border-radius: 0;
  font-family: "Poppins", -apple-system, BlinkMacSystemFont, "Segoe UI",
    sans-serif;
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(212, 161, 186, 0.15);
  position: sticky;
  top: 10px;
  z-index: 1000;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  animation: fadeInDown 0.6s ease-out;
  margin-left:50px;
}

/* Add a subtle scroll effect */
.navbar.scrolled {
  background: linear-gradient(
    135deg,
    rgba(68, 41, 63, 0.98),
    rgba(108, 70, 85, 0.95)
  );
  box-shadow: 0 2px 20px rgba(51, 34, 46, 0.4), 0 1px 3px rgba(0, 0, 0, 0.2);
}

.logo-container {
  font-size: 1.1rem;
  font-family: "Playfair Display", Georgia, serif;
  font-weight: 600;
  color: #f8f4f6;
  letter-spacing: 1px;
  text-decoration: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem 0;
}

.logo-container:hover {
  color: #ffffff;
  transform: translateY(-1px);
}

.logo-stack {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex-direction: row;
}

.logo {
  height: 42px;
  width: auto;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.logo-container:hover .logo {
  transform: scale(1.05);
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.15));
}

.nav-logo {
  font-size: 1rem;
  color: #f0e6ed;
  font-weight: 500;
  letter-spacing: 0.5px;
}

.navbar-left {
  padding-left: 1.5rem;
  display: flex;
  align-items: center;
}

.nav-links {
  list-style: none;
  display: flex;
  gap: 0.5rem;
  align-items: center;
  margin: 0;
  padding: 0;
}

.nav-links li {
  position: relative;
}

.nav-links li:not(:last-child)::after {
  content: "";
  position: absolute;
  right: -0.25rem;
  top: 50%;
  transform: translateY(-50%);
  width: 1px;
  height: 16px;
  background: linear-gradient(
    to bottom,
    transparent,
    rgba(212, 161, 186, 0.3),
    transparent
  );
}

.nav-links a {
  color: #e8d4e3;
  font-weight: 500;
  font-size: 0.95rem;
  text-transform: none;
  letter-spacing: 0.3px;
  padding: 0.75rem 1.25rem;
  border-radius: 6px;
  text-decoration: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  display: block;
}

.nav-links a::before {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #d4a1ba, #e8d4e3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateX(-50%);
}

.nav-links a:hover,
.nav-links a:focus {
  color: #ffffff;
  background: rgba(212, 161, 186, 0.1);
  transform: translateY(-1px);
  outline: none;
}

.nav-links a:hover::before,
.nav-links a:focus::before {
  width: 80%;
}

.nav-actions {
  display: flex;
  align-items: center;
  gap: 1.25rem;
  padding-right: 1.5rem;
}

.nav-cart {
  font-size: 1.3rem;
  color: #e8d4e3;
  padding: 0.75rem;
  border-radius: 8px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-decoration: none;
  position: relative;
  background: rgba(212, 161, 186, 0.05);
  border: 1px solid rgba(212, 161, 186, 0.1);
}

.nav-cart:hover,
.nav-cart:focus {
  color: #ffffff;
  background: rgba(212, 161, 186, 0.15);
  border-color: rgba(212, 161, 186, 0.2);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(212, 161, 186, 0.2);
  outline: none;
}

.account-button {
  background: rgba(212, 161, 186, 0.05);
  border: 1px solid rgba(212, 161, 186, 0.1);
  color: inherit;
  padding: 0.5rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.account-button:hover,
.account-button:focus {
  background: rgba(212, 161, 186, 0.15);
  border-color: rgba(212, 161, 186, 0.2);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(212, 161, 186, 0.2);
  outline: none;
}

.account-icon {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  object-fit: cover;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  margin: 0;
  background: transparent;
  filter: brightness(1.1);
}

.account-button:hover .account-icon,
.account-button:focus .account-icon {
  filter: brightness(1.3);
}

.dropdown-toggle:focus {
  box-shadow: none !important;
  outline: none !important;
}

.btn-primary:focus,
.btn-primary.focus,
.btn-primary:active,
.btn-primary.active {
  background-color: transparent !important;
  border-color: transparent !important;
  box-shadow: none !important;
}

.dropdown-toggle::after {
  display: none;
}

.dropdown-menu {
  border-radius: 12px;
  border: 1px solid rgba(212, 161, 186, 0.2);
  background: rgba(68, 41, 63, 0.95);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  min-width: 160px;
  padding: 0.5rem 0;
  margin-top: 0.5rem;
}

.dropdown-menu .dropdown-item {
  color: #811b68;
  padding: 0.75rem 1.25rem;
  font-weight: 500;
  font-size: 0.9rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: none;
  background: transparent;
}

.dropdown-menu .dropdown-item:hover,
.dropdown-menu .dropdown-item:focus {
  background: rgba(212, 161, 186, 0.15);
  color: #ffffff;
  outline: none;
}

.logout-button {
  background: linear-gradient(135deg, #d474a1, #c15683);
  color: white;
  border: none;
  padding: 0.75rem 1.25rem;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  font-size: 0.9rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 8px rgba(212, 116, 161, 0.3);
}

.logout-button:hover {
  background: linear-gradient(135deg, #c15683, #a94a6d);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(212, 116, 161, 0.4);
}

.menu-toggle {
  display: none;
  background: rgba(212, 161, 186, 0.1);
  border: 1px solid rgba(212, 161, 186, 0.2);
  color: #e8d4e3;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0.75rem;
  border-radius: 8px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  z-index: 1001;
}

.menu-toggle:hover,
.menu-toggle:focus {
  color: #ffffff;
  background: rgba(212, 161, 186, 0.2);
  border-color: rgba(212, 161, 186, 0.3);
  transform: translateY(-1px);
  outline: none;
}

@media (max-width: 768px) {
  .navbar {
    padding: 1rem 1.5rem;
    min-height: 80px;
  }

  .menu-toggle {
    display: block;
  }

  .nav-links {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    flex-direction: column;
    background: linear-gradient(
      135deg,
      rgba(68, 41, 63, 0.98),
      rgba(108, 70, 85, 0.95)
    );
    padding: 1.5rem;
    gap: 0.5rem;
    border-radius: 0 0 20px 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    transform: translateY(-100%);
    opacity: 0;
    visibility: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 100;
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
    border-top: 1px solid rgba(212, 161, 186, 0.1);
  }

  .nav-links.active {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
  }

  .nav-links li {
    width: 100%;
    text-align: center;
  }

  .nav-links li:not(:last-child)::after {
    display: none;
  }

  .nav-links a {
    display: block;
    padding: 1rem;
    margin: 0.25rem 0;
    border-radius: 8px;
    font-size: 1rem;
    background: rgba(212, 161, 186, 0.05);
    border: 1px solid rgba(212, 161, 186, 0.1);
  }

  .nav-links a:hover {
    background: rgba(212, 161, 186, 0.15);
    transform: none;
  }

  .navbar-left {
    padding-left: 0;
  }

  .nav-actions {
    padding-right: 0;
    gap: 1rem;
  }
}

@media (max-width: 480px) {
  .navbar {
    min-height: 70px;
    padding: 1rem;
  }

  .logo {
    height: 36px;
  }

  .nav-logo {
    font-size: 0.9rem;
  }

  .nav-cart {
    font-size: 1.1rem;
    padding: 0.6rem;
  }

  .account-icon {
    width: 28px;
    height: 28px;
  }

  .nav-actions {
    gap: 0.75rem;
  }
}

/* Very small phones */
@media (max-width: 320px) {
  .navbar {
    min-height: 65px;
    padding: 0.75rem;
  }

  .logo {
    height: 32px;
  }

  .nav-logo {
    font-size: 0.85rem;
  }

  .menu-toggle {
    font-size: 1rem;
    padding: 0.6rem;
  }

  .nav-links.active {
    padding: 1rem;
  }

  .nav-links a {
    padding: 0.75rem;
    font-size: 0.9rem;
  }

  .nav-cart {
    font-size: 1rem;
    padding: 0.5rem;
  }

  .account-icon {
    width: 26px;
    height: 26px;
  }
}
.nav-notification {
  position: relative;
}

.notification-button {
  font-size: 1.3rem;
  color: #e8d4e3;
  padding: 0.75rem;
  border-radius: 8px;
  background: rgba(212, 161, 186, 0.05);
  border: 1px solid rgba(212, 161, 186, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.notification-button:hover {
  background: rgba(212, 161, 186, 0.15);
  border-color: rgba(212, 161, 186, 0.2);
  color: #ffffff;
  transform: translateY(-1px);
}

.notification-count {
  position: absolute;
  top: 4px;
  right: 4px;
  background: #ff4d6d;
  color: white;
  border-radius: 50%;
  padding: 2px 6px;
  font-size: 0.7rem;
  font-weight: bold;
}

.notification-dropdown {
  position: absolute;
  right: 0;
  top: 120%;
  background: rgba(68, 41, 63, 0.95);
  border: 1px solid rgba(212, 161, 186, 0.2);
  border-radius: 10px;
  padding: 0.75rem;
  min-width: 300px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
  z-index: 1001;
  backdrop-filter: blur(15px);
}

.notification-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  color: #f0e6ed;
  font-size: 0.9rem;
}

.notification-item:last-child {
  margin-bottom: 0;
}

.view-button {
  background: linear-gradient(135deg, #d474a1, #c15683);
  color: white;
  border: none;
  padding: 0.3rem 0.6rem;
  border-radius: 6px;
  font-size: 0.75rem;
  cursor: pointer;
  transition: 0.2s ease;
}

.view-button:hover {
  background: linear-gradient(135deg, #c15683, #a94a6d);
}

.notification-empty {
  color: #ccc;
  font-size: 0.85rem;
  text-align: center;
}
