.card-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 20px;
  padding: 40px;
}

.section-title {
  text-align: center;
  font-size: 2rem;
  font-family: "Franklin Gothic", sans-serif;
  color: #333;
  margin: 0 auto;
  padding-top: 20px;
  padding-bottom: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.section-title h1 {
  font-family: 'Pacifico', cursive;
  color: #5e412f;
  font-size: 3rem;
  margin-bottom: 1rem;
}

.custom-card {
  background-color: #fff1f561;
  width: 280px;
  max-height: 500px;
  border-radius: 10px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  transition: transform 0.2s ease;
  padding: 20px;
}

.custom-card:hover {
  transform: scale(1.03);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.card-image {
  width: 200px;
  height: 200px;
}

.card-image-align {
  display: flex;
  align-items: center;
  justify-content: center;
}

.card-body {
  padding: 16px;
}

.card-title {
  font-size: 1.4rem;
  margin-bottom: 10px;
  font-weight: bold;
  font-family: Franklin Gothic;
}

.card-description {
  font-size: 0.95rem;
  margin-bottom: 12px;
  color: black;
}

.card-price {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 16px;
}

.card-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.buy-now-btn,
.edit-btn,
.delete-btn {
  padding: 10px 15px;
  border: none;
  width: 100%;
  border-radius: 6px;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
  color: white;
}

.buy-now-btn {
  background-color: #cc4068b0;
}

.buy-now-btn:hover {
  background-color: #8e7ab5;
}

.edit-btn {
  background-color: #4682b4;
}

.edit-btn:hover {
  background-color: #5a9bd4;
}

.delete-btn {
  background-color: #d9534f;
}

.delete-btn:hover {
  background-color: #c9302c;
}

.add-card {
  background-color: #fff1f561;
  width: 280px;
  max-height: 400px;
  border-radius: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: transform 0.2s ease;
  padding: 20px;
}

.add-card:hover {
  transform: scale(1.03);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.add-card-plus {
  font-size: 3.5rem;
  color: #cc4068b0;
  font-weight: bold;
  user-select: none;
}

.custom-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  width: 100vw;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.custom-modal {
  background-color: white;
  padding: 30px;
  width: 400px;
  min-height: 200px;
  border-radius: 10px;
  z-index: 10000;
  display: flex;
  flex-direction: column;
  gap: 15px;
}
.custom-modal input,
.custom-modal textarea {
  padding: 10px;
  border-radius: 6px;
  border: 1px solid #ccc;
  font-size: 1rem;
  width: 100%;
  resize: none;
  margin-bottom: 10px;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.save-btn,
.cancel-btn {
  padding: 10px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  transition: 0.2s ease;
  color: white;
}

.save-btn {
  background-color: #28a745;
}

.save-btn:hover {
  background-color: #218838;
}

.cancel-btn {
  background-color: #dc3545;
}

.cancel-btn:hover {
  background-color: #c82333;
}
