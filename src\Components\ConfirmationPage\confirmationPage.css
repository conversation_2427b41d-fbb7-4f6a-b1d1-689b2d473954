.confirmation-container {
  max-width: 600px;
  margin: 2rem auto;
  padding: 2rem;
  background-color: #fdfdfd;
  border-radius: 20px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  font-family: 'Segoe UI', sans-serif;
  text-align: center;
  color: #333;
  border: 1px solid rgba(212, 165, 154, 0.2);
  animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
 
.confirmation-icon {
  display: flex;
  justify-content: center;
  margin-bottom: 1.5rem;
}
 
.confirmation-icon i {
  font-size: 4rem;
  color: #d4a59a;
  background: linear-gradient(45deg, #d4a59a, #f8ae9d, #e6b8a2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 4px 8px rgba(212, 165, 154, 0.3);
  animation: lavishGlow 2s ease-in-out infinite alternate;
  filter: drop-shadow(0 2px 4px rgba(212, 165, 154, 0.4));
}
 
@keyframes lavishGlow {
  0% {
    transform: scale(1) rotate(-2deg);
    filter: drop-shadow(0 2px 4px rgba(212, 165, 154, 0.4)) brightness(1);
  }
  100% {
    transform: scale(1.05) rotate(2deg);
    filter: drop-shadow(0 4px 8px rgba(212, 165, 154, 0.6)) brightness(1.1);
  }
}
 
.confirmation-container h1 {
  color: #2d8659;
  margin-bottom: 0.5rem;
}
 
.confirmation-container p {
  margin: 0.4rem 0;
  line-height: 1.5;
}
 
.confirmation-details {
  text-align: left;
  background: linear-gradient(135deg, #f8f8f8, #f5f5f5);
  padding: 1.5rem;
  margin-top: 1rem;
  border-radius: 16px;
  border: 1px solid rgba(212, 165, 154, 0.15);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}
 
.confirmation-details p {
  margin: 0.3rem 0;
  font-size: 1rem;
}
 
.cancellation-note {
  margin-top: 1.5rem;
  padding: 1.2rem;
  background: linear-gradient(135deg, #fff8e1, #fff6e0);
  border-left: 4px solid #ffc107;
  border-radius: 12px;
  text-align: left;
  border: 1px solid rgba(255, 193, 7, 0.2);
  box-shadow: 0 3px 10px rgba(255, 193, 7, 0.1);
}
 

button {
  margin-top: 2rem;
  padding: 0.875rem 1.75rem;
  font-size: 0.95rem;
  font-weight: 600;
  background: linear-gradient(135deg, #d4a59a, #c49489);
  color: white;
  border: none;
  border-radius: 18px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 14px rgba(212, 165, 154, 0.35);
  text-transform: uppercase;
  letter-spacing: 0.8px;
  font-family: 'Poppins', sans-serif;
  border: 2px solid rgba(255, 255, 255, 0.25);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

button:hover {
  background: linear-gradient(135deg, #c49489, #b8877c);
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 8px 20px rgba(212, 165, 154, 0.45);
  border-color: rgba(255, 255, 255, 0.4);
}

button:active {
  transform: translateY(-1px) scale(0.98);
  box-shadow: 0 3px 10px rgba(212, 165, 154, 0.35);
  transition: all 0.1s ease;
}

button:focus {
  outline: none;
  box-shadow: 0 4px 14px rgba(212, 165, 154, 0.35), 0 0 0 3px rgba(212, 165, 154, 0.3);
}


button::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transition: width 0.6s, height 0.6s, top 0.6s, left 0.6s;
  transform: translate(-50%, -50%);
  z-index: 0;
}

button:active::before {
  width: 300px;
  height: 300px;
}

button span {
  position: relative;
  z-index: 1;
  display: inline-block;
  transition: transform 0.2s ease;
}

button.loading {
  pointer-events: none;
  opacity: 0.8;
}

button.loading span {
  opacity: 0.7;
}

button.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: buttonSpin 1s linear infinite;
  z-index: 3;
}

@keyframes buttonSpin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}


.confirmation-actions button:hover span {
  transform: translateY(-1px);
}

.confirmation-actions {
  display: flex;
  gap: 1.2rem;
  justify-content: center;
  margin-top: 2.5rem;
  flex-wrap: wrap;
  padding: 0.5rem;
}

.confirmation-actions button {
  margin-top: 0;
  min-width: 160px;
  position: relative;
  overflow: hidden;
  flex: 1;
  max-width: 200px;
}


.confirmation-actions button:nth-child(1) {
  background: linear-gradient(135deg, #2d8659, #256f4c, #1e5a3d);
  box-shadow: 0 4px 14px rgba(45, 134, 89, 0.35);
  border-color: rgba(45, 134, 89, 0.3);
}

.confirmation-actions button:nth-child(1):hover {
  background: linear-gradient(135deg, #256f4c, #1e5a3d, #175230);
  box-shadow: 0 8px 20px rgba(45, 134, 89, 0.5);
  border-color: rgba(45, 134, 89, 0.5);
}

.confirmation-actions button:nth-child(1):focus {
  box-shadow: 0 4px 14px rgba(45, 134, 89, 0.35), 0 0 0 3px rgba(45, 134, 89, 0.3);
}

.confirmation-actions button:nth-child(2) {
  background: linear-gradient(135deg, #d4a59a, #c49489, #b8877c);
  box-shadow: 0 4px 14px rgba(212, 165, 154, 0.35);
  border-color: rgba(212, 165, 154, 0.3);
}

.confirmation-actions button:nth-child(2):hover {
  background: linear-gradient(135deg, #c49489, #b8877c, #a67a6f);
  box-shadow: 0 8px 20px rgba(212, 165, 154, 0.5);
  border-color: rgba(212, 165, 154, 0.5);
}

.confirmation-actions button:nth-child(2):focus {
  box-shadow: 0 4px 14px rgba(212, 165, 154, 0.35), 0 0 0 3px rgba(212, 165, 154, 0.3);
}

.confirmation-actions button:nth-child(3) {
  background: linear-gradient(135deg, #8b4513, #a0522d, #b8651f);
  box-shadow: 0 4px 14px rgba(139, 69, 19, 0.35);
  border-color: rgba(139, 69, 19, 0.3);
}

.confirmation-actions button:nth-child(3):hover {
  background: linear-gradient(135deg, #a0522d, #b8651f, #cd7f32);
  box-shadow: 0 8px 20px rgba(139, 69, 19, 0.5);
  border-color: rgba(139, 69, 19, 0.5);
}

.confirmation-actions button:nth-child(3):focus {
  box-shadow: 0 4px 14px rgba(139, 69, 19, 0.35), 0 0 0 3px rgba(139, 69, 19, 0.3);
}



.confirmation-actions button:nth-child(1)::after {
  
  margin-right: 0.6rem;
  font-size: 1.1em;
  position: relative;
  z-index: 2;
}

.confirmation-actions button:nth-child(2)::after {
 
  margin-right: 0.6rem;
  font-size: 1.1em;
  position: relative;
  z-index: 2;
}

.confirmation-actions button:nth-child(3)::after {
  
  margin-right: 0.6rem;
  font-size: 1.1em;
  position: relative;
  z-index: 2;
}
 

.order-items {
  margin: 1.5rem 0;
  background: linear-gradient(135deg, #fafafa, #f9f9f9);
  border-radius: 16px;
  padding: 1.5rem;
  border: 1px solid rgba(212, 165, 154, 0.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}
 
.order-items h3 {
  margin-bottom: 1rem;
  color: #2d8659;
  font-size: 1.1rem;
}
 
.order-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  margin: 0.5rem 0;
  border-bottom: 1px solid #e0e0e0;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.5);
  transition: all 0.2s ease;
}

.order-item:hover {
  background: rgba(212, 165, 154, 0.1);
  transform: translateX(4px);
}
 
.order-item:last-child {
  border-bottom: none;
}
 
.item-details {
  display: flex;
  flex-direction: column;
  gap: 0.2rem;
}
 
.item-name {
  font-weight: 500;
  color: #333;
}
 
.item-quantity {
  font-size: 0.9rem;
  color: #666;
}
 
.item-price {
  font-weight: 600;
  color: #2d8659;
}
 
.delivery-info {
  margin-top: 0.8rem;
  padding: 1rem;
  background: linear-gradient(135deg, #e8f5e8, #f0f8f0);
  border-radius: 12px;
  border-left: 4px solid #2d8659;
  border: 1px solid rgba(45, 134, 89, 0.15);
  box-shadow: 0 3px 10px rgba(45, 134, 89, 0.1);
}
 
.delivery-info p {
  margin: 0.2rem 0;
}
 
.order-status {
  margin: 1.5rem 0;
  padding: 1.2rem;
  background: linear-gradient(135deg, #e8f5e8, #f0f8f0);
  border-radius: 16px;
  border-left: 4px solid #2d8659;
  text-align: center;
  border: 1px solid rgba(45, 134, 89, 0.15);
  box-shadow: 0 4px 12px rgba(45, 134, 89, 0.1);
}
 
.order-status p {
  margin: 0.3rem 0;
}
 
.order-status p:first-child {
  font-weight: 600;
  color: #2d8659;
}
 

@media (max-width: 480px) {
  .confirmation-container {
    padding: 1.2rem;
    border-radius: 16px;
    margin: 1rem;
  }
 
  .confirmation-icon i {
    font-size: 3rem;
  }
 
  .confirmation-details {
    font-size: 0.95rem;
  }
 
  .confirmation-actions {
    flex-direction: column;
    gap: 1rem;
    padding: 1rem;
  }

  .confirmation-actions button {
    width: 100%;
    min-width: unset;
    max-width: unset;
    padding: 1rem 1.5rem;
    font-size: 1rem;
    border-radius: 16px;
  }
 
  .order-items {
    padding: 1rem;
  }
 
  .order-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
    padding: 1rem 0;
  }
 
  .item-details {
    width: 100%;
  }
 
  .item-price {
    align-self: flex-end;
    font-size: 1.1rem;
  }
 
  .delivery-info {
    padding: 0.6rem;
  }
 
  .order-status {
    padding: 0.8rem;
  }
}
 

@media print {
  .confirmation-container {
    max-width: 100%;
    margin: 0;
    padding: 2rem;
    background-color: white !important;
    box-shadow: none !important;
    border-radius: 0 !important;
    color: black !important;
    font-family: 'Times New Roman', serif;
  }
 
  .confirmation-icon {
    margin-bottom: 1rem;
  }
 
  .confirmation-icon i {
    color: black !important;
    background: none !important;
    -webkit-text-fill-color: black !important;
    text-shadow: none !important;
    animation: none !important;
    filter: none !important;
    font-size: 2.5rem;
  }
 
  .confirmation-container h1 {
    color: black !important;
    font-size: 2rem;
    text-align: center;
    margin-bottom: 1.5rem;
    border-bottom: 2px solid black;
    padding-bottom: 0.5rem;
  }
 
  .confirmation-container p {
    color: black !important;
    font-size: 1rem;
    line-height: 1.6;
  }
 
  .confirmation-details {
    background: white !important;
    border: 2px solid black !important;
    padding: 1.5rem;
    margin: 1.5rem 0;
    border-radius: 0 !important;
  }
 
  .confirmation-details p {
    margin: 0.8rem 0;
    font-size: 1.1rem;
    color: black !important;
  }
 
  .cancellation-note {
    background-color: #f5f5f5 !important;
    border: 1px solid black !important;
    border-left: 4px solid black !important;
    padding: 1rem;
    margin: 1.5rem 0;
    border-radius: 0 !important;
    color: black !important;
  }
 
  
  .confirmation-actions {
    display: none !important;
  }
 

  .order-items {
    background: white !important;
    border: 1px solid black !important;
    padding: 1rem;
    margin: 1rem 0;
    border-radius: 0 !important;
  }
 
  .order-items h3 {
    color: black !important;
    border-bottom: 1px solid black;
    padding-bottom: 0.5rem;
    margin-bottom: 1rem;
  }
 
  .order-item {
    border-bottom: 1px dotted black !important;
    padding: 0.5rem 0;
  }
 
  .item-name, .item-quantity, .item-price {
    color: black !important;
  }
 
  .delivery-info {
    background: white !important;
    border: 1px solid black !important;
    border-left: 3px solid black !important;
    padding: 0.8rem;
    margin: 1rem 0;
    border-radius: 0 !important;
  }
 
  .order-status {
    background: white !important;
    border: 1px solid black !important;
    border-left: 3px solid black !important;
    padding: 0.8rem;
    margin: 1rem 0;
    border-radius: 0 !important;
  }
 
  .order-status p {
    color: black !important;
  }
 
  
  .confirmation-container::before {
    content: "ORDER CONFIRMATION RECEIPT";
    display: block;
    text-align: center;
    font-size: 0.9rem;
    font-weight: bold;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid black;
    letter-spacing: 2px;
  }
 
 
  .confirmation-container::after {
    content: "Thank you for choosing Sippin' Pretty! Please keep this confirmation for your records.";
    display: block;
    text-align: center;
    font-size: 0.8rem;
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid black;
    font-style: italic;
  }
 
 
  .confirmation-container {
    page-break-inside: avoid;
  }
 
  .confirmation-details {
    page-break-inside: avoid;
  }
}
 
 
 