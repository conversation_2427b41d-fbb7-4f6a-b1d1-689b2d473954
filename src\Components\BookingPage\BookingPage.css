

.booking-container {
  max-width: 700px;
  margin: 2rem auto;
  padding: 2rem;
  background: rgba(245, 206, 206, 0.15);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
  border-radius: 12px;
  font-family: 'Poppins', sans-serif;
  color: #333;
}

.booking-header h1 {
  margin-bottom: 0.5rem;
  color: #83598b;
  font-weight: 700;
  font-size: 2.4rem;
  letter-spacing: -0.5px;
}

.booking-header p {
  margin-top: 0;
  color: rgba(126, 59, 59, 0.75);
  font-size: 1.1rem;
  font-weight: 500;
  line-height: 1.4;
}

.booking-form {
  margin-top: 2rem;
  display: flex;
  flex-direction: column;
  gap: 1.8rem;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  font-weight: 600;
  margin-bottom: 0.6rem;
  color: rgba(63, 27, 54, 0.85);
  font-size: 0.95rem;
  letter-spacing: 0.3px;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 0.8rem 1rem;
  font-size: 1rem;
  border: 1.5px solid rgba(3, 1, 1, 0.6);
  border-radius: 8px;
  transition: all 0.3s ease;
  font-family: inherit;
  resize: vertical;
  background-color: rgba(255, 255, 255, 0.8);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #ca72b6;
  box-shadow: 0 0 0 3px rgba(202, 114, 182, 0.2);
  background-color: rgba(255, 255, 255, 0.95);
}

.form-row {
  display: flex;
  gap: 1.2rem;
}

.form-row .form-group {
  flex: 1;
}

.available-tables {
  background: #fffeff29;
  border: 1.5px solid #c985b8;
  padding: 1rem 1.2rem;
  border-radius: 8px;
}

.available-tables h2 {
  margin-top: 0;
  font-size: 1.3rem;
  color: #b973a7;
  font-weight: 700;
  margin-bottom: 0.8rem;
}

.available-tables ul {
  list-style: none;
  padding-left: 0;
  margin: 0;
  max-height: 160px;
  overflow-y: auto;
}

.available-tables li {
  margin-bottom: 0.6rem;
}

.available-tables label {
  cursor: pointer;
  font-weight: 600;
 
}

.available-tables input[type="radio"] {
  margin-right: 0.6rem;
  cursor: pointer;
}

.error-message {
  color: #cc0000;
  font-size: 0.85rem;
  margin-top: 0.3rem;
  font-weight: 600;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-5px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Selected table styling */
.selected-table {
  font-weight: 700;
  color: #83598b;
}

/* Character count for textarea */
.char-count {
  text-align: right;
  font-size: 0.8rem;
  color: #777;
  margin-top: 0.2rem;
}

/* Optional field label */
.optional {
  font-size: 0.8rem;
  font-weight: normal;
  color: #777;
  font-style: italic;
}

/* Validation success indicator */
.form-group input:valid:not(.input-error):not([value=""]),
.form-group select:valid:not(.input-error):not([value=""]) {
  border-left: 3px solid #4CAF50;
}

/* Error styling */
.input-error {
  border-color: #cc0000 !important;
  background-color: rgba(255, 0, 0, 0.05);
}

.total-price {
  margin-top: 1rem;
  font-size: 1.3rem;
  font-weight: 700;
  color: #99009198;
  display: flex;
  align-items: baseline;
  gap: 0.5rem;
}

.total-price small {
  font-weight: 400;
  font-size: 0.9rem;
  color: #bb9bc2;
}

button[type="submit"] {
  background-color: rgba(142, 91, 179, 0.8);
  color: white;
  font-weight: 600;
  font-size: 1.1rem;
  border: none;
  border-radius: 8px;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 1rem;
  letter-spacing: 0.5px;
}

button[type="submit"]:hover:not(:disabled) {
  background-color: #9c27b0;
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(156, 39, 176, 0.3);
}

button[type="submit"]:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
  opacity: 0.7;
}
 

/* Responsive adjustments */
@media (max-width: 600px) {
  .form-row {
    flex-direction: column;
  }
}
