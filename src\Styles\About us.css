


.page-center {
    display: flex;
    justify-content: center;
    padding: 40px 20px; 
    background-color: #ffffff;
}


.heading {
    font-family: 'Pacifico', cursive;
  color: #5e412f;
  font-size: 3rem;
  margin-bottom: 1rem;
    text-align: center;
    padding-top: 50px;
}


.paragraph {
  position: relative;
  font-family: 'Poppins', sans-serif;
  text-align: center;
  font-size: 1.3rem;
  margin: 0 auto 12px auto; 
  color: black;
  width: 50%;
}



.circle-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
}


.circle-image {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid #cc4068b0;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}


.container-wrapper {
    display: flex;
    justify-content: center;
    gap: 32px;
    flex-wrap: wrap;
    margin: 40px 0;
    padding: 0 20px;
}


.container {
    position: relative;
    width: 320px;
    min-height: 380px;
    background:
        linear-gradient(145deg, #ee9c9c 0%, #bcaec5 50%, #e4c0e6 100%);
    border-radius: 24px;
    box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.08),
        0 8px 25px rgba(0, 0, 0, 0.06),
        0 3px 10px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 32px 24px 28px 24px;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    border: 2px solid rgba(204, 64, 104, 0.08);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    isolation: isolate;
}

.container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg,
        transparent 0%,
        rgba(204, 64, 104, 0.6) 20%,
        rgba(204, 64, 104, 0.8) 50%,
        rgba(204, 64, 104, 0.6) 80%,
        transparent 100%);
    border-radius: 24px 24px 0 0;
    opacity: 0;
    transition: opacity 0.4s ease;
}

.container:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow:
        0 32px 80px rgba(0, 0, 0, 0.12),
        0 16px 40px rgba(0, 0, 0, 0.08),
        0 8px 20px rgba(0, 0, 0, 0.06),
        0 4px 12px rgba(204, 64, 104, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
    border-color: rgba(204, 64, 104, 0.25);
}

.container:hover::before {
    opacity: 1;
}

.container .circle-wrapper {
    margin-bottom: 20px;
    padding: 0;
    position: relative;
}

.container .circle-wrapper::before {
    content: '';
    position: absolute;
    top: -8px;
    left: -8px;
    right: -8px;
    bottom: -8px;
    background: linear-gradient(135deg,
        rgba(204, 64, 104, 0.1) 0%,
        rgba(204, 64, 104, 0.05) 50%,
        rgba(204, 64, 104, 0.1) 100%);
    border-radius: 50%;
    opacity: 0;
    transition: opacity 0.4s ease;
    z-index: -1;
}

.container:hover .circle-wrapper::before {
    opacity: 1;
}

.container .circle-image {
    width: 140px;
    height: 140px;
    border-radius: 50%;
    object-fit: cover;
    border: 5px solid #cc4068;
    box-shadow:
        0 12px 30px rgba(204, 64, 104, 0.25),
        0 6px 15px rgba(0, 0, 0, 0.1),
        inset 0 2px 4px rgba(255, 255, 255, 0.3);
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
    z-index: 1;
}

.container:hover .circle-image {
    transform: scale(1.08) rotate(2deg);
    box-shadow:
        0 20px 50px rgba(204, 64, 104, 0.35),
        0 10px 25px rgba(0, 0, 0, 0.15),
        inset 0 2px 4px rgba(255, 255, 255, 0.4);
    border-color: #b8365a;
}

.container h1 {
    font-family: 'Poppins', sans-serif;
    font-size: 1.5rem;
    font-weight: 700;
    color: #1a202c;
    margin: 12px 0 8px 0;
    text-align: center;
    line-height: 1.2;
    letter-spacing: -0.02em;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    transition: color 0.3s ease;
}

.container:hover h1 {
    color: #2d3748;
}

.container-text {
    font-family: 'Poppins', sans-serif;
    font-size: 1rem;
    font-weight: 500;
    color: #4a5568;
    text-align: center;
    line-height: 1.5;
    margin-top: auto;
    padding: 12px 16px;
    background:
        linear-gradient(135deg,
            rgba(204, 64, 104, 0.08) 0%,
            rgba(204, 64, 104, 0.04) 50%,
            rgba(204, 64, 104, 0.08) 100%);
    border-radius: 16px;
    border: 1.5px solid rgba(204, 64, 104, 0.15);
    -webkit-backdrop-filter: blur(5px);
    backdrop-filter: blur(5px);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.container-text::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent 0%,
        rgba(255, 255, 255, 0.4) 50%,
        transparent 100%);
    transition: left 0.6s ease;
}

.container:hover .container-text {
    background:
        linear-gradient(135deg,
            rgba(204, 64, 104, 0.12) 0%,
            rgba(204, 64, 104, 0.08) 50%,
            rgba(204, 64, 104, 0.12) 100%);
    border-color: rgba(204, 64, 104, 0.25);
    color: #2d3748;
    transform: translateY(-2px);
}

.container:hover .container-text::before {
    left: 100%;
}

/* Responsive design for containers */
@media (max-width: 768px) {
    .container-wrapper {
        gap: 16px;
    }

    .container {
        width: 240px;
        min-height: 280px;
        padding: 20px 16px 16px 16px;
    }

    .container .circle-image {
        width: 100px;
        height: 100px;
    }

    .container h1 {
        font-size: 1.2rem;
    }

    .container-text {
        font-size: 0.9rem;
    }
}

@media (max-width: 480px) {
    .container-wrapper {
        gap: 12px;
    }

    .container {
        width: 200px;
        min-height: 260px;
        padding: 16px 12px 12px 12px;
    }

    .container .circle-image {
        width: 80px;
        height: 80px;
        border-width: 3px;
    }

    .container h1 {
        font-size: 1.1rem;
    }

    .container-text {
        font-size: 0.85rem;
        padding: 6px 10px;
    }
}
