.user-admin-container {
  max-width: 900px;
  margin: 2rem auto;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 15px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
  font-family: "Poppins", sans-serif;
}

.user-admin-container h2 {
  font-family: "Pacifico", cursive;
  font-size: 2.2rem;
  color: #5e412f;
  margin-bottom: 1.5rem;
  text-align: center;
}

/* Add User Form */
.add-user-form {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: rgba(248, 187, 208, 0.1);
  border-radius: 10px;
  border: 1px solid rgba(248, 187, 208, 0.3);
}

.add-user-form input,
.add-user-form select {
  flex: 1;
  min-width: 200px;
  padding: 12px 16px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 1rem;
  font-family: inherit;
  transition: all 0.3s ease;
}

.add-user-form input:focus,
.add-user-form select:focus {
  outline: none;
  border-color: #f48fb1;
  box-shadow: 0 0 0 3px rgba(244, 143, 177, 0.2);
}

.add-user-form button {
  padding: 12px 24px;
  background: linear-gradient(to right, #f8bbd0, #f9c9d4);
  border: none;
  border-radius: 8px;
  color: #5e412f;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  flex: 1;
  min-width: 120px;
  box-shadow: 0 4px 8px rgba(244, 143, 177, 0.3);
}

.add-user-form button:hover {
  background: linear-gradient(to right, #f48fb1, #f9a8d4);
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(244, 143, 177, 0.4);
}

/* User Table */
.user-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0 8px;
}

.user-table th {
  padding: 12px 16px;
  text-align: left;
  font-weight: 600;
  color: #5e412f;
  border-bottom: 2px solid #f8bbd0;
}

.user-table td {
  padding: 12px 16px;
  background: white;
}

.user-table tr td:first-child {
  border-top-left-radius: 8px;
  border-bottom-left-radius: 8px;
}

.user-table tr td:last-child {
  border-top-right-radius: 8px;
  border-bottom-right-radius: 8px;
}

.user-table .delete-btn {
  padding: 8px 16px;
  background-color: #d9534f;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.user-table .delete-btn:hover {
  background-color: #c9302c;
}

@media (max-width: 768px) {
  .user-admin-container {
    padding: 1.5rem;
    margin: 1.5rem;
  }

  .user-admin-container h2 {
    font-size: 1.8rem;
  }

  .add-user-form {
    padding: 1rem;
  }
}

@media (max-width: 480px) {
  .user-admin-container {
    padding: 1rem;
    margin: 1rem;
  }

  .user-admin-container h2 {
    font-size: 1.5rem;
  }

  .add-user-form {
    flex-direction: column;
    padding: 0.8rem;
  }

  .add-user-form input,
  .add-user-form select,
  .add-user-form button {
    width: 100%;
    min-width: 0;
  }

  .user-table {
    display: block;
    overflow-x: auto;
    white-space: nowrap;
  }

  .user-table th,
  .user-table td {
    padding: 8px 12px;
  }
}

@media (max-width: 320px) {
  .user-admin-container {
    padding: 0.8rem;
    margin: 0.5rem;
  }

  .user-admin-container h2 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
  }

  .add-user-form {
    padding: 0.6rem;
    gap: 8px;
  }

  .add-user-form input,
  .add-user-form select {
    padding: 8px 12px;
    font-size: 0.9rem;
  }

  .add-user-form button {
    padding: 8px 16px;
    font-size: 0.9rem;
  }
}
.add-staff-button {
  padding: 10px 20px;
  background-color: #d4a59a;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.add-staff-button:hover {
  background-color: #d4a59a;
}
