.card-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 15px;
  padding: 20px;
}

.-section-title {
  text-align: center;
  font-size: 1.5rem;
  margin: 0 auto;
  padding-top: 40px;
  padding-bottom: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.-section-title h1 {
  font-family: "Pacifico", cursive;
  color: #5e412f;
  font-size: 3rem;
  margin-bottom: 1rem;
}

.custom-card {
  background-color: #fff1f561;
  width: 100%;
  max-width: 280px;
  border-radius: 10px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  transition: transform 0.2s ease;
  padding: 15px;
}

.custom-card:hover {
  transform: scale(1.03);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.card-image {
  width: 150px;
  height: 150px;
}
.card-image-allign {
  display: flex;
  align-items: center;
  justify-content: center;
}

.card-body {
  padding: 16px;
}

.card-title {
  font-size: 1.4rem;
  margin-bottom: 10px;
  font-weight: bold;
  font-family: Franklin Gothic;
}

.card-description {
  font-size: 0.95rem;
  margin-bottom: 12px;
  color: black;
}

.card-price {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 16px;
}

.buy-now-btn {
  padding: 10px 15px;
  background-color: #cc4068b0;
  color: white;
  border: none;
  width: 100%;
  border-radius: 6px;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.buy-now-btn:hover {
  background-color: #8e7ab5;
}

/* Tablet and larger */
@media (min-width: 768px) {
  .card-container {
    gap: 20px;
    padding: 40px;
  }

  .section-title {
    font-size: 2rem;
  }

  .section-title h1 {
    max-width: 500px;
    padding: 20px;
    font-size: inherit;
  }

  .card-image {
    width: 200px;
    height: 200px;
  }
}

/* Small mobile */
@media (max-width: 360px) {
  .card-container {
    padding: 10px;
  }

  .section-title h1 {
    max-width: 250px;
    padding: 10px;
    font-size: 1.1rem;
  }

  .custom-card {
    padding: 10px;
  }

  .card-image {
    width: 120px;
    height: 120px;
  }
}
.cart-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  background-color: #cc4068b0; 
  color: white;
  padding: 12px 20px;
  border-radius: 8px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
  z-index: 1000;
  animation: fade-in-out 3s ease;
}

@keyframes fade-in-out {
  0% {
    opacity: 0;
    transform: translateY(-10px);
  }
  10% {
    opacity: 1;
    transform: translateY(0);
  }
  90% {
    opacity: 1;
  }
  100% {
    opacity: 0;
    transform: translateY(-10px);
  }
}
