@import url("https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap");
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}
body {
  font-family: "Poppins", sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  display: flex;
  justify-content: center;

  min-height: 100vh;
  background-color: #f8bbd0;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, "Courier New",
    monospace;
}

.Wrapper {
  width: 500px;
  color: white;
  border-radius: 10px;
  padding: 30px 40px;
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 3px 10px rgba(14, 20, 13, 0.938);
  margin: 50px auto;
}
.Wrapper h1 {
  text-align: center;
  font-size: 30px;
  color: rgba(68, 41, 63, 0.85);
}
.Wrapper p {
  text-align: center;
  font-size: 15px;
  color: rgb(17, 17, 17);
  font-weight: 600;
}
.Wrapper .Inputbox {
  position: relative;
  width: 100%;
  height: 50px;
  margin: 30px 0;
  border: none;
  border-bottom: 2px solid #363434;
}
.Inputbox input {
  width: 100%;
  height: 100%;
  padding: 20px 45px 20px 20px;
  font-size: 20px;
  font-weight: 500;
  color: rgb(7, 7, 7);
  border-bottom: 1px solid gray;
  border: none;
  outline: none;
  border-radius: 5px;
  background: transparent;
}
.Inputbox input:hover {
  position: relative;
  display: flex;
  cursor: pointer;
  text-decoration: solid;
  background-color: rgb(178, 40, 233);
  transition: background-color 0.3s ease;
}
.Wrapper button:hover {
  text-decoration: underline;
  background-color: pink;
}
.Inputbox input::placeholder {
  color: rgb(17, 17, 17);
  text-align: centr;
  font-size: 13px;
  font-weight: 550;
}
.Wrapper button {
  width: 100%;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  color: rgb(13, 13, 14);
  margin: auto;
  font-weight: 600;
  font-size: 18px;
  border-radius: 2px;
  outline: none;
  border: none;
  background-color: rgba(68, 41, 63, 0.85);
}

.react-datepicker-wrapper {
  width: 100%;
}

.react-datepicker__input-container input {
  width: 100%;
  height: 50px;
  padding: 10px 20px;
  font-size: 16px;
  font-weight: 500;
  color: #111;
  border: none;
  border-radius: 5px;
  background-color: transparent;
  border-bottom: 1px solid gray;
  outline: none;
}

.react-datepicker__input-container input:hover {
  background-color: rgb(178, 40, 233);
  transition: background-color 0.3s ease;
  cursor: pointer;
}


.input-error {
  border-color: #e53935 !important;
  background-color: #ffebee;
}

.error-message {
  color: #e53935;
  font-size: 12px;
  margin-top: 4px;
  margin-bottom: 15px;
  font-weight: 500;
  line-height: 1.4;
  word-wrap: break-word;
}
