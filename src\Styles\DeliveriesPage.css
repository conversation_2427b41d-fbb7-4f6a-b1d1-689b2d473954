.deliveries-container {
  max-width: 1200px;
  margin: 2rem auto;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 15px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
  font-family: "Poppins", sans-serif;
}

.deliveries-container h2 {
  font-family: "Pacifico", cursive;
  font-size: 2.2rem;
  color: #5e412f;
  margin-bottom: 1.5rem;
  text-align: center;
}

.deliveries-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0 8px;
}

.deliveries-table th {
  padding: 12px 16px;
  text-align: left;
  font-weight: 600;
  color: #5e412f;
  border-bottom: 2px solid #f8bbd0;
}

.deliveries-table td {
  padding: 12px 16px;
  background: white;
}

.deliveries-table tr td:first-child {
  border-top-left-radius: 8px;
  border-bottom-left-radius: 8px;
}

.deliveries-table tr td:last-child {
  border-top-right-radius: 8px;
  border-bottom-right-radius: 8px;
}

.complete-btn {
  background-color: #81c784;
  border: none;
  padding: 8px 12px;
  color: white;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  transition: background-color 0.3s ease;
}

.complete-btn:hover {
  background-color: #388e3c;
}

@media (max-width: 768px) {
  .deliveries-container {
    padding: 1.5rem;
    margin: 1.5rem;
  }

  .deliveries-container h2 {
    font-size: 1.8rem;
  }
}

@media (max-width: 480px) {
  .deliveries-container {
    padding: 1rem;
    margin: 1rem;
  }

  .deliveries-container h2 {
    font-size: 1.5rem;
  }

  .deliveries-table {
    display: block;
    overflow-x: auto;
    white-space: nowrap;
  }

  .deliveries-table th,
  .deliveries-table td {
    padding: 8px 12px;
  }
}
